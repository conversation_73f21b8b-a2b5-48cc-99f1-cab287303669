{"name": "dex3-automation", "version": "1.0.0", "main": "index.js", "scripts": {"test": "npx playwright test", "test:headed": "npx playwright test --headed", "test:ui": "npx playwright test --ui", "test:search": "playwright test tests/tests/searchTest.spec.ts", "test:filter": "playwright test tests/tests/filterTest.spec.ts", "test:dashboard": "playwright test tests/tests/dashboardTest.spec.ts", "test:buysell": "playwright test tests/tests/buySellTest.spec.ts", "report": "npx playwright show-report", "report:open": "npx playwright show-report --browser", "test:reset": "RESET_LOGIN=true npx playwright test", "test:report": "npx playwright test && npx playwright show-report --browser", "test:dashboard:report": "npx playwright test tests/tests/dashboardTest.spec.ts && npx playwright show-report --browser", "setup": "node setup.js", "teardown": "node teardown.js", "redis:get": "node getDataFromRedis.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@playwright/test": "^1.51.1", "@types/node": "^22.7.8", "@types/nodemailer": "^6.4.17", "nodemailer": "^6.10.1", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.6.3"}, "dependencies": {"axios": "^1.11.0", "chalk": "^5.6.0", "dex3-automation": "file:", "ioredis": "^5.7.0", "redis": "^4.7.0"}}