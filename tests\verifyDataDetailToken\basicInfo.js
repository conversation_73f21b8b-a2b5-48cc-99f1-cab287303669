const axios = require('axios');
const fs = require('fs');
const path = require('path');

// === CONFIGURATION ===
const CONFIG = {
  TOKEN_ADDRESS: '6kjzL6NBtwhzdhZJASqHMimfaxNBGqKd2h6JHnnbRuao',
  TIMEOUT: 30000, // 30 seconds
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
  OUTPUT_DIR: 'test-results' // Thư mục lưu kết quả
};

// === SAFE CHALK (optional) ===
let _chalk = null;
try {
  // chalk v5 (ESM) khi require sẽ trả về { default: chalk }
  const mod = require('chalk');
  _chalk = (mod && (mod.default || mod)) || null;
} catch {}

const color =
  (name) => (s) => (_chalk && typeof _chalk[name] === 'function' ? _chalk[name](s) : s);

const C = {
  y: color('yellow'),
  r: color('red'),
  g: color('green'),
  b: color('bold'),
};

// === API CLIENTS ===
class APIClient {
  constructor(baseURL, defaultHeaders = {}) {
    this.client = axios.create({
      baseURL,
      timeout: CONFIG.TIMEOUT,
      headers: defaultHeaders
    });
  }

  async request(config, retries = CONFIG.MAX_RETRIES) {
    try {
      const response = await this.client(config);
      return response.data;
    } catch (error) {
      if (retries > 0 && this.shouldRetry(error)) {
        console.warn(`🔄 Retrying request... (${CONFIG.MAX_RETRIES - retries + 1}/${CONFIG.MAX_RETRIES})`);
        await this.delay(CONFIG.RETRY_DELAY);
        return this.request(config, retries - 1);
      }
      throw error;
    }
  }

  shouldRetry(error) {
    return error.code === 'ECONNABORTED' ||
      (error.response && [429, 500, 502, 503, 504].includes(error.response.status));
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Solscan API Client
const solscanClient = new APIClient('https://api-v2.solscan.io', {
  'accept': 'application/json, text/plain, */*',
  'origin': 'https://solscan.io',
  'referer': 'https://solscan.io/',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36'
});

// DEX3 API Client (không dùng authorization/cookie)
const dex3Client = new APIClient('https://api.dex3.ai', {
  'accept': 'application/json, text/plain, */*',
  'Content-Type': 'application/json',
  'origin': 'https://beta.dex3.ai',
  'referer': 'https://beta.dex3.ai/',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
});

// === API SERVICES ===
class SolscanService {
  static async getTokenInfo(address) {
    return await solscanClient.request({
      method: 'GET',
      url: `/v2/account?address=${address}&view_as=token`
    });
  }

  static async getHolderCount(address) {
    return await solscanClient.request({
      method: 'GET',
      url: `/v2/token/holder/total?address=${address}`
    });
  }

  static async getHoldersDetail(address, pageSize = 10, page = 1) {
    return await solscanClient.request({
      method: 'GET',
      url: `/v2/token/holders?address=${address}&page_size=${pageSize}&page=${page}`
    });
  }

  static async getPoolsInfo(address) {
    return await solscanClient.request({
      method: 'GET',
      url: `/v2/token/pools?page=1&page_size=100&token[]=${address}`
    });
  }
}

class Dex3Service {
  static async getTokenBasicInfo(address) {
    return await dex3Client.request({
      method: 'POST',
      url: '/v2/token-detail/basic-info',
      data: { address },
      headers: { clienttimestamp: Date.now().toString() }
    });
  }

  static async getHoldersInfo(address) {
    return await dex3Client.request({
      method: 'POST',
      url: '/v2/token-detail/holders',
      data: { address, tag: "all" },
      headers: { clienttimestamp: Date.now().toString() }
    });
  }
}

// === DATA PROCESSORS ===
class DataProcessor {
  static excludedWallets = [
    '********************************************', // Pump.fun AMM Pool
    'HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC'  // Meteora Pool
  ];

  static processSolscanData(tokenResponse, holderResponse, poolsResponse, solscanTop10Holdings = 0) {
    const tokenData = tokenResponse?.data;
    const metadata = tokenResponse?.metadata;
    const pools = poolsResponse?.data || [];

    if (!tokenData) {
      console.log("⚠️ Không tìm thấy dữ liệu token từ Solscan API");
      return null;
    }

    const poolStats = this.calculatePoolStats(pools);

    const tokenInfo = tokenData.tokenInfo || {};
    const metadataData = tokenData.metadata?.data || {};
    const ownExtensions = tokenInfo.ownExtensions || {};

    const decimals = tokenInfo.decimals || 0;
    const rawSupply = tokenInfo.supply ? parseInt(tokenInfo.supply) : 0;
    const totalSupply = decimals > 0 ? rawSupply / Math.pow(10, decimals) : rawSupply;
    const price = metadata?.tokens?.[CONFIG.TOKEN_ADDRESS]?.price_usdt || 0;

    return {
      // cơ bản
      symbol: metadataData.symbol || 'N/A',
      name: metadataData.name || 'N/A',
      description: ownExtensions.description || 'N/A',
      website: ownExtensions.website || 'N/A',
      twitter: ownExtensions.twitter || 'N/A',
      telegram: 'N/A',

      // thị trường
      price,
      liquidity: poolStats.totalTvl,
      fdv: 'N/A',
      marketCap: totalSupply * price,

      // 24h (Solscan không có → N/A)
      h24PriceChangePct: 'N/A',
      h24Volume: 'N/A',
      h24Traders: 'N/A',
      h24TradersNet: 'N/A',
      h24BuyTraderPct: 'N/A',
      h24HolderChangePct: 'N/A',

      // on-chain
      createdAt: tokenInfo.created_time ?
        new Date(tokenInfo.created_time * 1000).toISOString() : 'N/A',
      totalSupply,
      holderCount: holderResponse?.data || 0,
      ammWallet: 'N/A',

      // smart
      smartTrade: 'N/A',
      smartTraders: 'N/A',

      // tỷ lệ
      top10HoldingsPct: solscanTop10Holdings > 0 ? `${solscanTop10Holdings.toFixed(2)}%` : "N/A",
      devHoldingsPct: 'N/A',
      sniperHoldingsPct: 'N/A',
      insiderHoldingsPct: 'N/A',

      // dex/dev/lp
      dexPaid: 'N/A',
      devWallets: 'N/A',
      lpLock: 'N/A',
      lpBurnt: 'N/A',
      lpBurnAndEarn: 'N/A',

      // tương thích cũ
      totalVolume24h: poolStats.totalVolume24h,
      numTraders24h: poolStats.numTraders24h,
      creator: metadataData.creators?.[0]?.address || 'N/A',
    };
  }

  static processDex3Data(basicInfoResponse, holdersResponse) {
    const root = basicInfoResponse || {};
    const basicInfo = root?.data || root;
    if (!basicInfo) {
      console.log("⚠️ Không tìm thấy dữ liệu token từ DEX3 API");
      return null;
    }

    const baseToken = basicInfo.baseToken || {};
    const tokenInfo = basicInfo.tokenInfo || {};
    const h24Data  = basicInfo.data?.h24 || basicInfo.h24 || null;

    const pct = (v) => {
      if (v === null || v === undefined || isNaN(v)) return 'N/A';
      const n = Number(v);
      return (n >= 0 && n <= 1) ? n * 100 : n; // chuẩn hóa về 0..100
    };

    // chuyển đổi các chỉ số 24h thành trường riêng
    const priceChgPct24 = (typeof h24Data?.priceChange === 'number') ? h24Data.priceChange * 100 : 'N/A';
    const buyTraderPct24 = (typeof h24Data?.buyTraderPct === 'number') ? pct(h24Data.buyTraderPct) : 'N/A';
    const holderChgPct24 = (typeof h24Data?.holderChangePct === 'number') ? pct(h24Data.holderChangePct) : 'N/A';

    return {
      // cơ bản
      symbol:      baseToken.symbol || 'N/A',
      name:        baseToken.name || 'N/A',
      description: baseToken.description || 'N/A',
      website:     baseToken.website || 'N/A',
      twitter:     baseToken.twitter || 'N/A',
      telegram:    baseToken.telegram || 'N/A',

      // thị trường
      price:       basicInfo.price || 0,
      liquidity:   basicInfo.liquidity || 0,
      fdv:         basicInfo.fdv || 0,
      marketCap:   basicInfo.marketCap || 0,

      // 24h (tách hàng)
      h24PriceChangePct: priceChgPct24,
      h24Volume:  h24Data?.volume ?? 'N/A',
      h24Traders: h24Data?.traders ?? 'N/A',
      h24TradersNet: (typeof h24Data?.tradersNet === 'number') ? h24Data.tradersNet : 'N/A',
      h24BuyTraderPct: buyTraderPct24,
      h24HolderChangePct: holderChgPct24,

      // on-chain
      createdAt:   basicInfo.createdAt || 'N/A',
      totalSupply: basicInfo.totalSupply || 0,
      holderCount: basicInfo.holderCount || 0,
      ammWallet:   basicInfo.ammVaultAddress || 'N/A',

      // smart
      smartTrade:  'N/A', // không có trường "smartTrade" trong response mẫu
      smartTraders: tokenInfo.smartTraders ?? 'N/A',

      // tỷ lệ
      top10HoldingsPct: pct(tokenInfo.top10HoldersPct),
      devHoldingsPct:   pct(tokenInfo.devHoldingsPct),
      sniperHoldingsPct:pct(tokenInfo.sniperHoldingsPct),
      insiderHoldingsPct:pct(tokenInfo.insiderHoldingsPct),

      // dex/dev/lp
      dexPaid:     tokenInfo.dexPaid ?? 'N/A',
      devWallets:  Array.isArray(tokenInfo.devs) ? tokenInfo.devs.join(', ') : 'N/A',
      lpLock:         tokenInfo.lpLock ?? basicInfo.lpLock ?? 'N/A',
      lpBurnt:        tokenInfo.lpBurnt ?? basicInfo.lpBurnt ?? 'N/A',
      lpBurnAndEarn:  tokenInfo.lpBurnAndEarn ?? basicInfo.lpBurnAndEarn ?? 'N/A',

      // tương thích cũ
      totalVolume24h: h24Data?.volume || 0,
      numTraders24h:  h24Data?.traders || 0,
      creator:        Array.isArray(tokenInfo.devs) ? tokenInfo.devs[0] : 'N/A',
    };
  }

  static calculatePoolStats(pools = []) {
    return pools.reduce((acc, pool) => ({
      totalVolume24h: acc.totalVolume24h + (pool.total_volume_24h || 0),
      totalTvl: acc.totalTvl + (pool.total_tvl || 0),
      numTraders24h: acc.numTraders24h + (pool.num_trader_24h || 0)
    }), { totalVolume24h: 0, totalTvl: 0, numTraders24h: 0 });
  }

  static calculateTop10Holdings(holdersResponse) {
    if (!holdersResponse) return 'Không có dữ liệu holder';

    let holdersData = null;
    if (holdersResponse.data?.data?.length) {
      holdersData = holdersResponse.data.data;
    } else if (holdersResponse.data?.length) {
      holdersData = holdersResponse.data;
    } else if (holdersResponse.holders?.length) {
      holdersData = holdersResponse.holders;
    } else if (Array.isArray(holdersResponse)) {
      holdersData = holdersResponse;
    }

    if (!holdersData?.length) return 'Không có dữ liệu holder';

    const filteredHolders = holdersData.filter(holder =>
      !this.excludedWallets.includes(holder.address)
    );

    const top10Holders = filteredHolders.slice(0, 10);

    const totalHoldings = top10Holders.reduce((accumulator, holder) => {
      const holdingPct = parseFloat(holder.holdingPct || holder.holding_pct || holder.percentage) || 0;
      return accumulator + holdingPct;
    }, 0);

    console.log("\n📊 === PHÂN TÍCH TOP 10 HOLDERS ===");
    console.log(`💰 Tổng % nắm giữ của Top 10 (sau khi lọc): ${totalHoldings.toFixed(4)}%`);
    console.log("====================================\n");

    return totalHoldings;
  }

  static calculateSolscanTop10Holdings(solscanHoldersResponse, totalSupply) {
    if (!solscanHoldersResponse?.data?.length || !totalSupply) return 0;

    const filteredHolders = solscanHoldersResponse.data.filter(holder =>
      !this.excludedWallets.includes(holder.owner)
    );

    const top10Holders = filteredHolders.slice(0, 10);

    let totalTop10Holdings = 0;

    top10Holders.forEach((holder) => {
      const actualAmount = holder.amount / Math.pow(10, holder.decimals);
      const holdingPercentage = (actualAmount / totalSupply) * 100;
      totalTop10Holdings += holdingPercentage;
    });

    return totalTop10Holdings;
  }
}

// === FILE UTILITIES ===
class FileUtils {
  static ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`📁 Đã tạo thư mục: ${dirPath}`);
    }
  }

  static generateFileName(tokenAddress, timestamp = null) {
    const ts = timestamp || new Date().toISOString().replace(/[:.]/g, '-');
    return `token-analysis-${tokenAddress}-${ts}.json`;
  }

  static saveResultsToJSON(data, tokenAddress) {
    try {
      this.ensureDirectoryExists(CONFIG.OUTPUT_DIR);

      const fileName = this.generateFileName(tokenAddress);
      const filePath = path.join(CONFIG.OUTPUT_DIR, fileName);

      const jsonData = JSON.stringify(data, null, 2);
      fs.writeFileSync(filePath, jsonData, 'utf8');

      console.log(C.g(`💾 Đã lưu kết quả vào file: ${filePath}`));
      return filePath;
    } catch (error) {
      console.error(C.r(`❌ Lỗi khi lưu file JSON: ${error.message}`));
      return null;
    }
  }

  static createTestReport(solscanData, dex3Data, tokenAddress, comparison) {
    const timestamp = new Date().toISOString();

    return {
      metadata: {
        tokenAddress,
        timestamp,
        testVersion: '1.0.0',
        description: 'So sánh dữ liệu token giữa Solscan API và DEX3 API'
      },
      rawData: {
        solscan: solscanData,
        dex3: dex3Data
      },
      comparison: {
        totalDifference: comparison.totalDifference,
        totalFields: comparison.totalFields,
        mismatches: comparison.mismatches,
        summary: {
          significantMismatches: comparison.mismatches.filter(m => parseFloat(m.percentDiff) > 5).length,
          totalMismatches: comparison.mismatches.length,
          accuracyPercentage: Math.max(0, 100 - comparison.totalDifference).toFixed(2)
        }
      },
      statistics: {
        fieldsCompared: comparison.totalFields,
        fieldsMatched: comparison.totalFields - comparison.mismatches.length,
        fieldsWithSignificantDifference: comparison.mismatches.filter(m => parseFloat(m.percentDiff) > 5).length
      }
    };
  }
}

// === DISPLAY UTILITIES ===
class DisplayUtils {
  static formatNumber(value, options = {}) {
    if (typeof value !== 'number' || isNaN(value)) return value;
    return value.toLocaleString(undefined, options);
  }

  static formatPercentage(value) {
    return typeof value === 'number' ? `${value.toFixed(2)}%` : value;
  }

  static normalizePct(val) {
    if (val === null || val === undefined || isNaN(val)) return 'N/A';
    const n = Number(val);
    const pct = (n >= 0 && n <= 1) ? n * 100 : n;
    return `${pct.toFixed(2)}%`;
  }

  static yesNo(val) {
    if (val === undefined || val === null) return 'N/A';
    const b = (val === true || val === 1 || val === '1');
    return b ? 'Yes' : 'No';
  }

  static createComparisonTable(solscanData, dex3Data) {
    const F = this;

    const fields = [
      // cơ bản
      { key: 'symbol',      label: 'Ký hiệu' },
      { key: 'name',        label: 'Tên Token' },
      { key: 'website',     label: 'Website' },
      { key: 'twitter',     label: 'Twitter' },
      { key: 'telegram',    label: 'Telegram' },

      // thị trường
      { key: 'price',       label: 'Giá (USD)' },
      { key: 'liquidity',   label: 'Tổng TVL (USD)', formatter: (v) => F.formatNumber(v, { maximumFractionDigits: 0 }) },
      { key: 'fdv',         label: 'FDV (USD)',      formatter: (v) => F.formatNumber(v, { maximumFractionDigits: 0 }) },
      { key: 'marketCap',   label: 'Market Cap (USD)', formatter: (v) => F.formatNumber(v, { maximumFractionDigits: 0 }) },

      // 24h (tách hàng)
      { key: 'h24PriceChangePct', label: '% Price change (24h)', formatter: (v) => (typeof v === 'number' ? F.formatPercentage(v) : 'N/A') },
      { key: 'h24Volume',         label: 'Volume (24h)',         formatter: (v) => (typeof v === 'number' ? F.formatNumber(v, { maximumFractionDigits: 0 }) : 'N/A') },
      { key: 'h24Traders',        label: 'Traders (24h)',        formatter: (v) => (typeof v === 'number' ? F.formatNumber(v) : 'N/A') },
      { key: 'h24TradersNet',     label: 'Traders Net (24h)',    formatter: (v) => (typeof v === 'number' ? (v >= 0 ? `+${v}` : `${v}`) : 'N/A') },
      { key: 'h24BuyTraderPct',   label: '% Buy traders (24h)',  formatter: (v) => (typeof v === 'number' ? F.formatPercentage(v) : 'N/A') },
      { key: 'h24HolderChangePct',label: '% Holder change (24h)',formatter: (v) => (typeof v === 'number' ? F.formatPercentage(v) : 'N/A') },

      // on-chain
      { key: 'createdAt',   label: 'Ngày tạo (UTC)', formatter: (v) => v?.replace?.('.000Z', 'Z') ?? v },
      { key: 'totalSupply', label: 'Tổng cung',        formatter: (v) => F.formatNumber(v) },
      { key: 'holderCount', label: 'Số người nắm giữ', formatter: (v) => F.formatNumber(v) },
      { key: 'ammWallet',   label: 'AMM Wallet' },

      // smart
      { key: 'smartTrade',   label: 'Smart Trade' },
      { key: 'smartTraders', label: 'Smart Traders', formatter: (v) => F.formatNumber(Number(v) || 0) },

      // tỷ lệ
      { key: 'top10HoldingsPct',  label: '% Top 10 Holders', formatter: (v) => {
          const n = typeof v === 'number' ? v : parseFloat(String(v).replace('%',''));
          return F.formatPercentage((isNaN(n) ? 0 : n));
        }
      },
      { key: 'devHoldingsPct',    label: '% Dev Holding',     formatter: (v) => F.normalizePct(v) },
      { key: 'sniperHoldingsPct', label: '% Sniper Holding',  formatter: (v) => F.normalizePct(v) },
      { key: 'insiderHoldingsPct',label: '% Insider Holding', formatter: (v) => F.normalizePct(v) },

      // dex/dev/lp
      { key: 'dexPaid',    label: 'DEX Paid',  formatter: (v) => F.yesNo(v) },
      { key: 'devWallets', label: 'Ví Dev' },
      { key: 'lpLock',        label: 'lpLock',        formatter: (v) => F.yesNo(v) },
      { key: 'lpBurnt',       label: 'lpBurnt',       formatter: (v) => F.yesNo(v) },
      { key: 'lpBurnAndEarn', label: 'lpBurnAndEarn', formatter: (v) => F.yesNo(v) },
    ];

    return fields.reduce((table, field) => {
      const solscanValueRaw = solscanData?.[field.key];
      const dex3ValueRaw    = dex3Data?.[field.key];

      const solscanValue = field.formatter
        ? field.formatter(solscanValueRaw)
        : solscanValueRaw;

      const dex3Value = field.formatter
        ? field.formatter(dex3ValueRaw)
        : dex3ValueRaw;

      table[field.label] = {
        'API Solscan': (solscanValue !== undefined ? solscanValue : 'N/A'),
        'API Dự án':   (dex3Value    !== undefined ? dex3Value    : 'N/A'),
      };
      return table;
    }, {});
  }

  static compareResults(solscanData, dex3Data) {
    if (!solscanData || !dex3Data) {
      return { totalDifference: 0, mismatches: [] };
    }

    // helper: parse % về con số 0..100
    const parsePct = (v) => {
      if (v === null || v === undefined) return 0;
      if (typeof v === 'string') {
        const n = parseFloat(v.replace('%',''));
        return isNaN(n) ? 0 : n;
      }
      const n = Number(v);
      if (isNaN(n)) return 0;
      return n <= 1 ? n * 100 : n;
    };

    const numericFields = [
      'price','liquidity','fdv','marketCap',
      'totalSupply','holderCount',
      'totalVolume24h','numTraders24h',
      'smartTraders',
      'top10HoldingsPct','devHoldingsPct','sniperHoldingsPct','insiderHoldingsPct',
      'h24PriceChangePct','h24Volume','h24Traders','h24TradersNet','h24BuyTraderPct','h24HolderChangePct'
    ];
    const percentLike = new Set([
      'top10HoldingsPct','devHoldingsPct','sniperHoldingsPct','insiderHoldingsPct',
      'h24PriceChangePct','h24BuyTraderPct','h24HolderChangePct'
    ]);

    const stringFields = [
      'name', 'symbol', 'website', 'twitter', 'telegram', 'creator'
    ];

    let totalDifferences = 0;
    let totalFields = 0;
    const mismatches = [];

    numericFields.forEach(field => {
      const solscanRaw = solscanData[field];
      const dex3Raw = dex3Data[field];

      const solscanValue = percentLike.has(field)
        ? parsePct(solscanRaw)
        : (parseFloat(solscanRaw) || 0);

      const dex3Value = percentLike.has(field)
        ? parsePct(dex3Raw)
        : (parseFloat(dex3Raw) || 0);

      if (solscanValue > 0 || dex3Value > 0) {
        totalFields++;
        const maxValue = Math.max(solscanValue, dex3Value);
        const difference = Math.abs(solscanValue - dex3Value);
        const percentDiff = maxValue > 0 ? (difference / maxValue) * 100 : 0;

        totalDifferences += percentDiff;

        if (percentDiff >= 5) {
          mismatches.push({
            field,
            type: 'numeric',
            solscanValue,
            dex3Value,
            percentDiff: percentDiff.toFixed(2)
          });
        }
      }
    });

    stringFields.forEach(field => {
      const solscanValue = String(solscanData[field] || '').trim();
      const dex3Value = String(dex3Data[field] || '').trim();

      if (solscanValue !== 'N/A' || dex3Value !== 'N/A') {
        totalFields++;
        if (solscanValue !== dex3Value) {
          totalDifferences += 100;
          mismatches.push({
            field,
            type: 'string',
            solscanValue,
            dex3Value,
            percentDiff: '100.00'
          });
        }
      }
    });

    const averageDifference = totalFields > 0 ? totalDifferences / totalFields : 0;

    return {
      totalDifference: averageDifference,
      mismatches,
      totalFields
    };
  }

  static logMismatchSummary(comparison) {
    const significantMismatches = comparison.mismatches.filter(mismatch =>
      parseFloat(mismatch.percentDiff) > 5
    );

    if (significantMismatches.length > 0) {
      console.log(C.b(C.y("\n⚠️ === CÁC TRƯỜNG DỮ LIỆU SAI LỆCH TRÊN 5% ===")));
      significantMismatches.forEach((mismatch, index) => {
        const fieldNames = {
          'name': 'Tên Token',
          'symbol': 'Ký hiệu',
          'website': 'Website',
          'twitter': 'Twitter',
          'telegram': 'Telegram',
          'totalSupply': 'Tổng cung',
          'holderCount': 'Số người nắm giữ',
          'price': 'Giá (USD)',
          'fdv': 'FDV (USD)',
          'marketCap': 'Market Cap (USD)',
          'totalVolume24h': 'Tổng Volume 24h (USD)',
          'liquidity': 'Tổng TVL (USD)',
          'numTraders24h': 'Số Lượng Traders 24h',
          'smartTraders': 'Smart Traders',
          'top10HoldingsPct': '% Top 10 Holders',
          'devHoldingsPct': '% Dev Holding',
          'sniperHoldingsPct': '% Sniper Holding',
          'insiderHoldingsPct': '% Insider Holding',
          'h24PriceChangePct': '% Price change (24h)',
          'h24Volume': 'Volume (24h)',
          'h24Traders': 'Traders (24h)',
          'h24TradersNet': 'Traders Net (24h)',
          'h24BuyTraderPct': '% Buy traders (24h)',
          'h24HolderChangePct': '% Holder change (24h)',
          'creator': 'Địa chỉ người tạo'
        };

        const line = `${index + 1}. ${fieldNames[mismatch.field] || mismatch.field}: Chênh lệch ${mismatch.percentDiff}%`;
        console.log(C.r(line));
      });
      console.log(C.b(C.y("===========================================\n")));
    }
  }

  static displayResults(solscanData, dex3Data) {
    console.log(`\n📊 Kết quả phân tích token: ${CONFIG.TOKEN_ADDRESS}\n`);

    if (solscanData || dex3Data) {
      const solscanDataSafe = solscanData || this.createEmptyDataStructure();
      const dex3DataSafe = dex3Data || this.createEmptyDataStructure();

      const comparisonTable = this.createComparisonTable(solscanDataSafe, dex3DataSafe);
      console.log("--- Bảng So Sánh Dữ Liệu Token ---");
      console.table(comparisonTable);

      const comparison = this.compareResults(solscanDataSafe, dex3DataSafe);
      this.logMismatchSummary(comparison);

      console.log("\n--- So Sánh Mô Tả Đầy Đủ ---");
      console.log("\n[API Solscan]");
      console.log(solscanDataSafe.description || "(Không có mô tả)");
      console.log("\n[API Dự án]");
      console.log(dex3DataSafe.description || "(Không có mô tả)");
      console.log("\n---------------------------------");
    } else {
      console.log("❌ Không có dữ liệu để hiển thị");
    }
  }

  static createEmptyDataStructure() {
    return {
      // cơ bản
      symbol: 'N/A',
      name: 'N/A',
      description: 'N/A',
      website: 'N/A',
      twitter: 'N/A',
      telegram: 'N/A',

      // thị trường
      price: 0,
      liquidity: 0,
      fdv: 0,
      marketCap: 0,

      // 24h
      h24PriceChangePct: 'N/A',
      h24Volume: 'N/A',
      h24Traders: 'N/A',
      h24TradersNet: 'N/A',
      h24BuyTraderPct: 'N/A',
      h24HolderChangePct: 'N/A',

      // on-chain
      createdAt: 'N/A',
      totalSupply: 0,
      holderCount: 0,
      ammWallet: 'N/A',

      // smart
      smartTrade: 'N/A',
      smartTraders: 0,

      // tỷ lệ
      top10HoldingsPct: 'N/A',
      devHoldingsPct: 'N/A',
      sniperHoldingsPct: 'N/A',
      insiderHoldingsPct: 'N/A',

      // dex/dev/lp
      dexPaid: 'N/A',
      devWallets: 'N/A',
      lpLock: 'N/A',
      lpBurnt: 'N/A',
      lpBurnAndEarn: 'N/A',

      // tương thích cũ
      totalVolume24h: 0,
      numTraders24h: 0,
      creator: 'N/A',
    };
  }
}

// === MAIN APPLICATION ===
class TokenAnalyzer {
  static async analyze(tokenAddress = CONFIG.TOKEN_ADDRESS) {
    try {
      console.log(`🔄 Bắt đầu phân tích token: ${tokenAddress}...`);
      console.log("📡 Đang lấy dữ liệu từ các API...");

      const [
        r1, r2, r3, r4, r5, r6
      ] = await Promise.allSettled([
        SolscanService.getTokenInfo(tokenAddress),
        SolscanService.getHolderCount(tokenAddress),
        SolscanService.getHoldersDetail(tokenAddress, 40, 1),
        SolscanService.getPoolsInfo(tokenAddress),
        Dex3Service.getTokenBasicInfo(tokenAddress),
        Dex3Service.getHoldersInfo(tokenAddress)
      ]);

      const solscanTokenResponse = r1.status === 'fulfilled' ? r1.value : null;
      const solscanHolderResponse = r2.status === 'fulfilled' ? r2.value : null;
      const solscanHoldersDetailResponse = r3.status === 'fulfilled' ? r3.value : null;
      const solscanPoolsResponse = r4.status === 'fulfilled' ? r4.value : null;
      const dex3BasicInfoResponse = r5.status === 'fulfilled' ? r5.value : null;
      const dex3HoldersResponse = r6.status === 'fulfilled' ? r6.value : null;

      [r5, r6].forEach((res, idx) => {
        if (res.status === 'rejected') {
          const name = idx === 0 ? 'DEX3 basic-info' : 'DEX3 holders';
          const st = res.reason?.response?.status;
          console.warn(`⚠️ Không lấy được ${name}${st ? ` (HTTP ${st})` : ''}. Tiếp tục với dữ liệu sẵn có.`);
        }
      });

      console.log("✅ Lấy dữ liệu (tối đa có thể) xong, đang xử lý...");

      // Top10% Solscan dùng totalSupply từ chính Solscan
      let solscanDerivedTotalSupply = 0;
      try {
        const tokenInfo = solscanTokenResponse?.data?.tokenInfo || {};
        const decimals = tokenInfo.decimals || 0;
        const rawSupply = tokenInfo.supply ? parseInt(tokenInfo.supply) : 0;
        solscanDerivedTotalSupply = decimals > 0 ? rawSupply / Math.pow(10, decimals) : rawSupply;
      } catch (_) {}

      const solscanTop10Holdings = DataProcessor.calculateSolscanTop10Holdings(
        solscanHoldersDetailResponse,
        solscanDerivedTotalSupply
      );

      const solscanData = DataProcessor.processSolscanData(
        solscanTokenResponse,
        solscanHolderResponse,
        solscanPoolsResponse,
        solscanTop10Holdings
      );

      const dex3Data = DataProcessor.processDex3Data(
        dex3BasicInfoResponse,
        dex3HoldersResponse
      );

      DisplayUtils.displayResults(solscanData, dex3Data);

      // Tạo báo cáo so sánh và lưu vào file JSON
      const comparison = DisplayUtils.compareResults(solscanData, dex3Data);
      const testReport = FileUtils.createTestReport(solscanData, dex3Data, tokenAddress, comparison);
      const savedFilePath = FileUtils.saveResultsToJSON(testReport, tokenAddress);

      console.log(C.g("✅ Phân tích hoàn tất!"));

      if (savedFilePath) {
        console.log(C.b(`📊 Báo cáo chi tiết đã được lưu tại: ${savedFilePath}`));
      }

      return {
        solscanData,
        dex3Data,
        comparison,
        testReport,
        savedFilePath
      };

    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  static handleError(error) {
    console.error("❌ Đã xảy ra lỗi!");

    if (error.response) {
      console.error(`API Error: ${error.response.status} ${error.response.statusText}`);
      console.error(`URL: ${error.config?.url}`);
      if (error.response.data) {
        console.error('Response:', JSON.stringify(error.response.data, null, 2));
      }
    } else if (error.request) {
      console.error('Network Error: Không nhận được phản hồi từ server');
    } else {
      console.error('Error:', error.message);
    }
  }
}

// === EXECUTION (CLI --address=<MINT> --output=<DIR>) ===
if (require.main === module) {
  let addr = CONFIG.TOKEN_ADDRESS;
  let outputDir = CONFIG.OUTPUT_DIR;

  const args = process.argv.slice(2);

  // Parse address argument
  const addressArg = args.find(a => a.startsWith('--address='));
  if (addressArg) {
    const v = addressArg.split('=')[1]?.trim();
    if (v) addr = v;
  }

  // Parse output directory argument
  const outputArg = args.find(a => a.startsWith('--output='));
  if (outputArg) {
    const v = outputArg.split('=')[1]?.trim();
    if (v) {
      CONFIG.OUTPUT_DIR = v;
      console.log(`📁 Sử dụng thư mục output tùy chỉnh: ${v}`);
    }
  }

  // Show help if requested
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
📖 Cách sử dụng:
  node basicInfo.js [options]

🔧 Tùy chọn:
  --address=<TOKEN_ADDRESS>  Địa chỉ token cần phân tích
  --output=<DIRECTORY>       Thư mục lưu kết quả JSON (mặc định: test-results)
  --help, -h                 Hiển thị hướng dẫn này

💡 Ví dụ:
  node basicInfo.js --address=6kjzL6NBtwhzdhZJASqHMimfaxNBGqKd2h6JHnnbRuao
  node basicInfo.js --address=ABC123 --output=my-results
    `);
    process.exit(0);
  }

  console.log(`🎯 Phân tích token: ${addr}`);
  console.log(`📁 Kết quả sẽ được lưu tại: ${CONFIG.OUTPUT_DIR}`);

  TokenAnalyzer.analyze(addr).catch(() => process.exit(1));
}

module.exports = { TokenAnalyzer, CONFIG };
