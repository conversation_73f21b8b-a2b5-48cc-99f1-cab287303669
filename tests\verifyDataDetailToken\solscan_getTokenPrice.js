#!/usr/bin/env node
/* Call Solscan token/price, print JSO<PERSON> and a day-by-day summary
   Usage:
     node solscan_getTokenPrice.js
     node solscan_getTokenPrice.js --address=So11111111111111111111111111111111111111112 --from=20250817
*/

const axios = require('axios');

const DEFAULT_ADDRESS = '6kjzL6NBtwhzdhZJASqHMimfaxNBGqKd2h6JHnnbRuao';
const DEFAULT_FROM    = '20250817'; // YYYYMMDD

// --- CLI args ---
const argv = process.argv.slice(2);
const getArg = (key, fallback) => {
  const k = `--${key}=`;
  const hit = argv.find(a => a.startsWith(k));
  return hit ? hit.slice(k.length) : fallback;
};
const address   = getArg('address', DEFAULT_ADDRESS);
const from_time = getArg('from', DEFAULT_FROM);

// --- helpers ---
function fmtDateYYYYMMDDtoDDMMYYYY(n) {
  // n: number like 20250823
  const s = String(n);
  if (s.length !== 8) return s; // fallback
  const y = s.slice(0, 4);
  const m = s.slice(4, 6);
  const d = s.slice(6, 8);
  return `${d}-${m}-${y}`;
}

function pctChange(curr, prev) {
  if (typeof curr !== 'number' || typeof prev !== 'number' || !isFinite(curr) || !isFinite(prev) || prev === 0) {
    return null;
  }
  return ((curr - prev) / prev) * 100;
}

function withPct(val) {
  return typeof val === 'number' && isFinite(val) ? `${val.toFixed(2)}%` : '—';
}

function fmtNum(val, maxFrac = 10) {
  if (typeof val !== 'number' || !isFinite(val)) return val;
  // giới hạn phần thập phân để không quá dài
  return Number(val.toFixed(Math.min(maxFrac, 10)));
}

// --- axios client ---
const client = axios.create({
  baseURL: 'https://api-v2.solscan.io',
  timeout: 30000,
  headers: {
    'accept': 'application/json, text/plain, */*',
    'origin': 'https://solscan.io',
    'referer': 'https://solscan.io/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  }
});

(async () => {
  try {
    const { data } = await client.get('/v2/token/price', {
      params: { 'address[]': address, from_time }
    });

    // In raw response (đủ để debug)
    console.log('--- Raw response ---');
    console.log(JSON.stringify(data, null, 2));

    // Lấy dãy giá theo address
    const key = Object.keys(data?.data || {})[0];
    const series = (key && Array.isArray(data.data[key])) ? data.data[key] : [];

    // Sort theo ngày tăng dần (phòng trường hợp API trả lộn xộn)
    series.sort((a, b) => (a.date || 0) - (b.date || 0));

    // Tạo bảng tóm tắt
    const rows = series.map((row, idx) => {
      const prev = idx > 0 ? series[idx - 1] : null;
      const change = prev ? pctChange(row.price, prev.price) : null;
      return {
        Date: fmtDateYYYYMMDDtoDDMMYYYY(row.date),
        Price: fmtNum(row.price, 10),
        'Change vs prev': withPct(change)
      };
    });

    console.log('\n--- Daily summary ---');
    console.table(rows);
  } catch (err) {
    const status = err?.response?.status;
    const msg = err?.response?.statusText || err.message;
    console.error('Request failed', status ? `(${status})` : '', '-', msg);
    if (err?.response?.data) {
      console.error('Response body:', JSON.stringify(err.response.data, null, 2));
    }
    process.exit(1);
  }
})();
