# Token Analysis Tool với JSON Export

## M<PERSON> tả
Tool này so sánh dữ liệu token giữa Solscan API và DEX3 API, hiển thị kết quả trên console và **tự động lưu kết quả chi tiết vào file JSON**.

## Tính năng mới: L<PERSON>u kết quả JSON
- ✅ Tự động lưu kết quả phân tích vào file JSON
- ✅ Bao gồm dữ liệu thô từ cả hai API
- ✅ Thống kê so sánh chi tiết
- ✅ Metadata về test (timestamp, version, token address)
- ✅ Tùy chọn thư mục output

## Cách sử dụng

### Cơ bản
```bash
node basicInfo.js
```
Sẽ phân tích token mặc định và lưu kết quả vào thư mục `test-results/`

### Với token tùy chỉnh
```bash
node basicInfo.js --address=YOUR_TOKEN_ADDRESS
```

### Vớ<PERSON> thư mục output tùy chỉnh
```bash
node basicInfo.js --output=my-custom-results
```

### Kết hợp các tùy chọn
```bash
node basicInfo.js --address=ABC123 --output=analysis-results
```

### Xem hướng dẫn
```bash
node basicInfo.js --help
```

## Cấu trúc file JSON output

File JSON được lưu sẽ có cấu trúc như sau:

```json
{
  "metadata": {
    "tokenAddress": "...",
    "timestamp": "2025-08-24T11:02:07.904Z",
    "testVersion": "1.0.0",
    "description": "So sánh dữ liệu token giữa Solscan API và DEX3 API"
  },
  "rawData": {
    "solscan": { /* Dữ liệu thô từ Solscan API */ },
    "dex3": { /* Dữ liệu thô từ DEX3 API */ }
  },
  "comparison": {
    "totalDifference": 43.26,
    "totalFields": 20,
    "mismatches": [
      {
        "field": "liquidity",
        "type": "numeric",
        "solscanValue": 50063.81,
        "dex3Value": 2212982.30,
        "percentDiff": "97.74"
      }
    ],
    "summary": {
      "significantMismatches": 10,
      "totalMismatches": 10,
      "accuracyPercentage": "56.73"
    }
  },
  "statistics": {
    "fieldsCompared": 20,
    "fieldsMatched": 10,
    "fieldsWithSignificantDifference": 10
  }
}
```

## Tên file output
File được đặt tên theo format:
```
token-analysis-{TOKEN_ADDRESS}-{TIMESTAMP}.json
```

Ví dụ:
```
token-analysis-6kjzL6NBtwhzdhZJASqHMimfaxNBGqKd2h6JHnnbRuao-2025-08-24T11-02-07-907Z.json
```

## Lợi ích của JSON export
1. **Lưu trữ lâu dài**: Có thể lưu trữ và so sánh kết quả theo thời gian
2. **Phân tích sau**: Có thể import vào các tool khác để phân tích
3. **Báo cáo**: Dễ dàng tạo báo cáo từ dữ liệu JSON
4. **Automation**: Có thể tích hợp vào pipeline CI/CD
5. **Debugging**: Dữ liệu thô giúp debug các vấn đề API

## Ví dụ sử dụng dữ liệu JSON

### Đọc file JSON trong Node.js
```javascript
const fs = require('fs');
const results = JSON.parse(fs.readFileSync('path/to/result.json', 'utf8'));

console.log('Accuracy:', results.comparison.summary.accuracyPercentage + '%');
console.log('Significant mismatches:', results.comparison.summary.significantMismatches);
```

### Phân tích nhiều file kết quả
```javascript
const glob = require('glob');
const files = glob.sync('test-results/*.json');

files.forEach(file => {
  const data = JSON.parse(fs.readFileSync(file, 'utf8'));
  console.log(`${data.metadata.tokenAddress}: ${data.comparison.summary.accuracyPercentage}%`);
});
```
